# app/controllers/api/v1/widget/conversation_tokens_controller.rb
class Api::V1::Widget::ConversationTokensController < Api::V1::Widget::BaseController

  # Body: { "conversation_id": 12345 }
  def create
    Rails.logger.info("###########################################################")
    Rails.logger.info("Fetching conversation")
    Rails.logger.info("###########################################################")
    conv = find_conversation!

    # Logging conversation id
    Rails.logger.info("###########################################################")
    Rails.logger.info("Conversation id: #{conv.id}")
    Rails.logger.info("###########################################################")

    payload = {
      conversation_id: conv.id,
      source_id: conv.contact_inbox.source_id,
      inbox_id: conv.contact_inbox.inbox_id,
    }
     # Logging conversation id
    Rails.logger.info("###########################################################")
    Rails.logger.info("Conversation payload: #{JSON.pretty_generate(payload)}")
    Rails.logger.info("###########################################################")

    token = ::Widget::TokenService.new(payload: payload).generate_token

    render json: { token: token }
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'Conversation not found' }, status: :not_found
  rescue ActiveSupport::MessageEncryptor::InvalidMessage, ArgumentError => e
    render json: { error: e.message }, status: :unprocessable_entity
  rescue StandardError => e
    Rails.logger.error("Token generation failed: #{e.message}")
    render json: { error: 'Token generation failed' }, status: :unprocessable_entity
  end

  private

  def find_conversation!
    # IMPORTANT: only allow looking up conversations that belong to THIS widget’s inbox and the current contact
    # If your UI sends display_id instead of id, adjust the lookup accordingly.
    # Prefer scoping via the inbox that BaseController already resolved.
    inbox = @web_widget&.inbox or raise ActiveRecord::RecordNotFound
    Rails.logger.info("###########################################################")
    Rails.logger.info("Inbox: #{inbox.id}")
    Rails.logger.info("###########################################################")

    request = Conversation
      .joins(:contact_inbox)
      .where(contact_inboxes: { inbox_id: inbox.id, contact_id: @contact.id })
      .where(id: conversation_id_param)
    # Print request
    Rails.logger.info("###########################################################")
    Rails.logger.info("Request sql #{request.to_sql}")
    Rails.logger.info("###########################################################")

    # return request
    request.first
  end



  def conversation_id_param
    # Strong params; assumes JSON body with { conversation_id: ... }
    params.require(:conversation_id)
  end
end
